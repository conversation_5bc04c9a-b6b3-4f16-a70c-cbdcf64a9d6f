#!/usr/bin/env python
"""
Fusion Indicator Validation Script

This script tests the fusion regime detection system on BTC/USDT data
to validate that our Python implementation produces similar results to TradingView.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_fetcher import fetch_ohlcv_data
from fusion.fusion_aggregator import FusionAggregator, FusionConfig
from fusion.fusion_visualizer import FusionVisualizer


def fetch_btc_data(period: str = "1y", interval: str = "1d") -> pd.DataFrame:
    """
    Fetch BTC/USDT data using existing data fetcher

    Args:
        period: Data period (1y, 2y, 5y, etc.)
        interval: Data interval (1d, 1h, etc.)

    Returns:
        OHLCV DataFrame
    """
    print(f"Fetching BTC data for period: {period}, interval: {interval}")

    try:
        # Calculate date range based on period
        end_date = datetime.now()
        if period == "1y":
            start_date = end_date - timedelta(days=365)
            limit = 365
        elif period == "2y":
            start_date = end_date - timedelta(days=730)
            limit = 730
        elif period == "6mo":
            start_date = end_date - timedelta(days=180)
            limit = 180
        else:
            start_date = end_date - timedelta(days=365)
            limit = 365

        # Use your existing data fetcher to get BTC data from Binance
        symbols = ['BTC/USDT']
        exchange_id = 'binance'

        print(f"Fetching BTC/USDT data from {start_date.date()} to {end_date.date()}")

        # Fetch data using your existing infrastructure
        data_dict = fetch_ohlcv_data(
            exchange_id=exchange_id,
            symbols=symbols,
            timeframe=interval,
            since=start_date,
            limit=limit,
            use_cache=True
        )

        if 'BTC/USDT' not in data_dict or data_dict['BTC/USDT'].empty:
            raise ValueError("No BTC/USDT data retrieved")

        data = data_dict['BTC/USDT']

        print(f"Retrieved {len(data)} data points from {data.index[0]} to {data.index[-1]}")
        return data

    except Exception as e:
        print(f"Error fetching data with existing fetcher: {e}")
        print("Creating synthetic test data instead...")

        # Create synthetic data as fallback
        days = 365 if period == "1y" else 730 if period == "2y" else 180
        dates = pd.date_range(start=datetime.now() - timedelta(days=days),
                             end=datetime.now(), freq='D')
        np.random.seed(42)  # For reproducible results

        # Generate realistic BTC-like price data
        initial_price = 45000
        returns = np.random.normal(0.001, 0.03, len(dates))  # Daily returns with some volatility
        prices = [initial_price]

        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        # Create OHLCV data
        data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, len(dates))
        }, index=dates)

        # Ensure high >= close >= low and high >= open >= low
        data['high'] = data[['open', 'close', 'high']].max(axis=1)
        data['low'] = data[['open', 'close', 'low']].min(axis=1)

        print(f"Synthetic test data: {len(data)} periods from {data.index[0]} to {data.index[-1]}")
        return data


def create_fusion_config(use_all_indicators: bool = True) -> FusionConfig:
    """
    Create fusion configuration matching PineScript defaults
    
    Args:
        use_all_indicators: Whether to enable all indicators
        
    Returns:
        FusionConfig object
    """
    config = FusionConfig(
        # Enable indicators as per PineScript defaults
        include_adx=True,  # Default disabled in PineScript
        include_kpss=False ,  # Default disabled in PineScript
        include_adf=True,   # Default enabled in PineScript
        include_pp=True,    # Default enabled in PineScript
        include_hurst=True, # Default enabled in PineScript
        include_corr=True,  # Default enabled in PineScript
        include_rpc=True,   # Default enabled in PineScript
        include_garch=True, # Default enabled in PineScript
        include_wavelet=True, # Default enabled in PineScript
        include_halflife=False ,  # Default disabled in PineScript
        
        # Thresholds matching PineScript
        trend_threshold=0.1,
        revert_threshold=-0.1,
        
        # Smoothing
        smoothing_length=7,
        retain_previous_signal=True,
        
        # Indicator parameters matching PineScript
        adx_di_length=28,
        adx_length=20,
        kpss_length=85,
        adf_lookback=110,
        pp_length=100,
        hurst_length=25,
        hurst_median_length=25,
        correlation_length=200,
        momentum_type="MACD",
        rpc_length=100,
        garch_length=30,
        wavelet_length=50,
        wavelet_smoothing=10,
        halflife_lookback=100
    )
    
    return config


def run_fusion_validation():
    """Main validation function"""
    print("=" * 60)
    print("FUSION REGIME DETECTION VALIDATION")
    print("=" * 60)
    
    try:
        # Fetch BTC data
        btc_data = fetch_btc_data(period="2y", interval="1d")
        print(f"Data shape: {btc_data.shape}")
        print(f"Date range: {btc_data.index[0]} to {btc_data.index[-1]}")
        
        # Create fusion configuration
        config = create_fusion_config(use_all_indicators=True)
        print(f"\nFusion Configuration:")
        print(f"- Enabled indicators: {sum([config.include_adx, config.include_kpss, config.include_adf, config.include_pp, config.include_hurst, config.include_corr, config.include_rpc, config.include_garch, config.include_wavelet, config.include_halflife])}")
        print(f"- Trend threshold: {config.trend_threshold}")
        print(f"- Revert threshold: {config.revert_threshold}")
        print(f"- Smoothing length: {config.smoothing_length}")
        
        # Initialize fusion system
        aggregator = FusionAggregator(config)
        visualizer = FusionVisualizer(aggregator)
        
        print("\nCalculating fusion signals...")
        
        # Calculate fusion results
        results = aggregator.calculate(btc_data)
        
        print("Fusion calculation completed!")
        
        # Print summary statistics
        print(f"\nSUMMARY STATISTICS:")
        print(f"- Individual signals calculated: {len(results['individual_signals'])}")
        
        for name, signal in results['individual_signals'].items():
            valid_count = signal.notna().sum()
            mean_val = signal.mean()
            std_val = signal.std()
            print(f"  {name.upper()}: {valid_count} valid values, mean={mean_val:.4f}, std={std_val:.4f}")
        
        # Aggregated signal stats
        agg_signal = results['aggregated_signal']
        smoothed_signal = results['smoothed_signal']
        regime_signal = results['regime_signal']
        
        print(f"\nAGGREGATED SIGNAL STATS:")
        print(f"- Valid values: {agg_signal.notna().sum()}")
        print(f"- Mean: {agg_signal.mean():.4f}")
        print(f"- Std: {agg_signal.std():.4f}")
        print(f"- Min: {agg_signal.min():.4f}")
        print(f"- Max: {agg_signal.max():.4f}")
        
        print(f"\nREGIME CLASSIFICATION:")
        regime_counts = regime_signal.value_counts().sort_index()
        total_periods = len(regime_signal)
        
        for regime_val, count in regime_counts.items():
            regime_name = {-1: "Mean Reverting", 0: "Neutral", 1: "Trending"}[regime_val]
            percentage = (count / total_periods) * 100
            print(f"- {regime_name}: {count} periods ({percentage:.1f}%)")
        
        # Create visualizations
        print(f"\nCreating visualizations...")
        
        # Create output directory
        output_dir = "fusion_validation_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate validation report
        saved_files = visualizer.create_validation_report(
            btc_data, 
            output_dir=output_dir
        )
        
        print(f"\nValidation complete! Files saved:")
        for plot_type, filename in saved_files.items():
            print(f"- {plot_type}: {filename}")
        
        # Save detailed results to CSV for manual comparison with TradingView
        results_df = pd.DataFrame({
            'date': smoothed_signal.index,
            'close_price': btc_data['close'].reindex(smoothed_signal.index),
            'aggregated_signal': agg_signal,
            'smoothed_signal': smoothed_signal,
            'regime_signal': regime_signal,
            'is_trending': results['is_trending'].astype(int),
            'is_reverting': results['is_reverting'].astype(int),
            'is_neutral': results['is_neutral'].astype(int)
        })
        
        # Add individual indicator signals
        for name, signal in results['individual_signals'].items():
            results_df[f'{name}_signal'] = signal.reindex(smoothed_signal.index)
        
        csv_filename = os.path.join(output_dir, f"fusion_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
        results_df.to_csv(csv_filename, index=False)
        print(f"- Detailed results: {csv_filename}")
        
        # Print recent values for manual verification
        print(f"\nRECENT VALUES (last 10 periods):")
        recent_data = results_df.tail(10)[['date', 'close_price', 'smoothed_signal', 'regime_signal']]
        print(recent_data.to_string(index=False, float_format='%.4f'))
        
        print(f"\n" + "=" * 60)
        print("VALIDATION COMPLETED SUCCESSFULLY!")
        print("Compare the generated plots and CSV data with TradingView output.")
        print("=" * 60)
        
        return results, btc_data
        
    except Exception as e:
        print(f"ERROR during validation: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    # Run validation
    results, data = run_fusion_validation()
    
    if results is not None:
        print("\nValidation script completed successfully!")
        print("Check the 'fusion_validation_output' directory for generated plots and data.")
    else:
        print("\nValidation script failed. Check the error messages above.")
