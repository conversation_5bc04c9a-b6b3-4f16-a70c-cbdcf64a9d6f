# src/strategy.py

import pandas as pd
import numpy as np
import logging
from typing import Dict, <PERSON><PERSON>, Optional, List

# Import the functions from the scoring module
from .scoring import calculate_pairwise_win_matrix, find_best_asset_for_day
# Import our new ratio-based comparison functions
from .indicators.ratio_indicators import calculate_daily_ratio_scores

def calculate_daily_scores(
    data_dict: Dict[str, pd.DataFrame],
    pgo_signal_col: str = 'pgo_signal',
    debug_matrices: bool = True,
    use_btc_pgo_signal: bool = False,  # Kept for backward compatibility
    btc_pgo_signal: Optional[int] = None,  # Kept for backward compatibility
    use_mtpi_signal: bool = False,
    mtpi_signals: Optional[pd.Series] = None,
    ratio_calculation: str = 'manual_inversion',  # New parameter for ratio calculation method
    # Fusion signal parameters
    use_fusion_signal: bool = False,
    fusion_trend_threshold: float = 0.1,
    fusion_revert_threshold: float = -0.1,
    fusion_smoothing_length: int = 14,
    fusion_retain_previous_signal: bool = True
) -> pd.DataFrame:
    """
    Calculates asset scores for each day based on PGO signals.

    Args:
        data_dict: Dictionary of DataFrames {symbol: DataFrame} with OHLCV and
                   signal data, indexed by timestamp. Assumes DataFrames
                   share a common, continuous time index.
        pgo_signal_col: The column name containing the signal binary value (0 or 1).
        debug_matrices: Whether to collect and save detailed matrix data for debugging.
        use_btc_pgo_signal: (Deprecated) Whether to use the BTC PGO signal to filter asset selection.
                           Use use_mtpi_signal instead.
        btc_pgo_signal: (Deprecated) The BTC PGO signal value. Use mtpi_signals instead.
        use_mtpi_signal: Whether to use the MTPI signal to filter asset selection.
                        If True, only invest when MTPI signal is positive (1).
        mtpi_signals: Time series of MTPI signals indexed by timestamp.
                     Only used if use_mtpi_signal is True.
        use_fusion_signal: Whether to use fusion regime detection to filter signals.
        fusion_trend_threshold: Threshold for trending regime detection.
        fusion_revert_threshold: Threshold for mean-reverting regime detection.
        fusion_smoothing_length: Smoothing length for fusion signal aggregation.
        fusion_retain_previous_signal: Enable hysteresis logic for fusion signals.

    Returns:
        pd.DataFrame: DataFrame containing daily scores, indexed by timestamp,
                      with columns for each asset symbol. Returns an empty
                      DataFrame if calculation fails or no common index exists.
                      If use_mtpi_signal is True and mtpi_signal is not 1,
                      all scores will be set to 0 (stay out of the market).
    """
    if not data_dict:
        logging.warning("calculate_daily_scores: Input data_dict is empty.")
        return pd.DataFrame()

    # Check which trend method to use from config
    try:
        from .config_manager import load_config
        config = load_config()
        trend_method = config.get('settings', {}).get('trend_method', 'RSI')

        # Use the appropriate ratio-based approach based on trend method
        if trend_method == 'PGO For Loop' or trend_method == 'PGO':
            logging.info(f"Using ratio-based approach with PGO ({trend_method})...")

            # Extract PGO parameters from config
            pgo_length = config.get('settings', {}).get('pgo_length', 35)
            pgo_upper_threshold = config.get('settings', {}).get('pgo_upper_threshold', 1.1)
            pgo_lower_threshold = config.get('settings', {}).get('pgo_lower_threshold', -0.58)
            close_col = 'close'  # Default

            # Import the function
            from .indicators.ratio_indicators import calculate_daily_pgo_scores

            # Call our function
            daily_scores_df, debug_data = calculate_daily_pgo_scores(
                data_dict=data_dict,
                pgo_length=pgo_length,
                close_col=close_col,
                debug=debug_matrices,
                long_threshold=pgo_upper_threshold,
                short_threshold=pgo_lower_threshold,
                ratio_calculation=ratio_calculation
            )
        elif trend_method == 'MTPI':
            logging.info(f"Using ratio-based approach with MTPI multi-indicator aggregation ({trend_method})...")

            # Extract MTPI configuration for asset trend detection
            asset_mtpi_config = config.get('settings', {}).get('asset_mtpi_indicators', {})

            # If no asset MTPI config, use default based on existing MTPI config
            if not asset_mtpi_config:
                logging.info("No asset_mtpi_indicators config found, using default MTPI configuration")
                mtpi_config = config.get('settings', {}).get('mtpi_indicators', {})
                asset_mtpi_config = {
                    'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score'],  # Use 3 indicators (odd number)
                    'combination_method': mtpi_config.get('combination_method', 'consensus'),
                    'long_threshold': mtpi_config.get('long_threshold', 0.1),
                    'short_threshold': mtpi_config.get('short_threshold', -0.1),
                    'pgo': mtpi_config.get('pgo', {
                        'length': 35,
                        'upper_threshold': 1.35,
                        'lower_threshold': -0.58
                    }),
                    'bollinger_bands': mtpi_config.get('bollinger_bands', {
                        'length': 33,
                        'multiplier': 2.0,
                        'long_threshold': 76.0,
                        'short_threshold': 31.0,
                        'use_heikin_ashi': False,
                        'heikin_src': 'close'
                    })
                }
                # Add other indicators from MTPI config if they exist
                for indicator in ['dwma_score', 'median_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']:
                    if indicator in mtpi_config:
                        asset_mtpi_config[indicator] = mtpi_config[indicator]

            close_col = 'close'  # Default

            # Import the function
            from .indicators.asset_mtpi_indicators import calculate_daily_mtpi_scores

            # Call our function
            daily_scores_df, debug_data = calculate_daily_mtpi_scores(
                data_dict=data_dict,
                mtpi_config=asset_mtpi_config,
                close_col=close_col,
                debug=debug_matrices
            )
        else:
            # Default to RSI method
            logging.info(f"Using ratio-based approach with TradingView RSI ({trend_method})...")

            # Extract parameters from the indicator configuration in the first dataframe
            rsi_length = 14  # Default
            sma_length = 14  # Default
            close_col = 'close'  # Default

            # Import the function
            from .indicators.ratio_indicators import calculate_daily_ratio_scores

            # Call our function
            daily_scores_df, debug_data = calculate_daily_ratio_scores(
                data_dict=data_dict,
                rsi_length=rsi_length,
                sma_length=sma_length,
                close_col=close_col,
                debug=debug_matrices
            )

        logging.info("Successfully calculated daily scores using ratio-based comparisons.")

        # For backward compatibility
        effective_use_mtpi = use_mtpi_signal or use_btc_pgo_signal

        # Get the latest MTPI signal for logging purposes only
        latest_mtpi_signal = None
        if effective_use_mtpi and mtpi_signals is not None and not mtpi_signals.empty:
            latest_mtpi_signal = mtpi_signals.iloc[-1]
            logging.info(f"Latest MTPI signal is {latest_mtpi_signal}")
        elif effective_use_mtpi and btc_pgo_signal is not None:
            latest_mtpi_signal = btc_pgo_signal
            logging.info(f"Using deprecated BTC PGO signal: {latest_mtpi_signal}")

        # We don't modify the scores here anymore - we'll handle the MTPI signal in the equity curve calculation
        if effective_use_mtpi:
            if latest_mtpi_signal is not None:
                if latest_mtpi_signal != 1:  # If MTPI signal is not bullish
                    logging.info(f"Latest MTPI signal is {latest_mtpi_signal}, will stay out of market during equity curve calculation")
                else:
                    logging.info("Latest MTPI signal is bullish (1), will proceed with normal asset selection")
            else:
                logging.warning("MTPI signal is None but use_mtpi_signal is True. Proceeding with normal asset selection.")

        # Save debug matrices to HTML if enabled
        if debug_matrices and debug_data:
            try:
                # Get the timeframe from the first dataframe's index frequency
                timeframe = '1h'  # Default
                if data_dict and len(data_dict) > 0:
                    first_df = next(iter(data_dict.values()))
                    if hasattr(first_df.index, 'freq') and first_df.index.freq is not None:
                        freq_str = str(first_df.index.freq)
                        if 'H' in freq_str:
                            timeframe = f"{freq_str.replace('H', '')}h"
                        elif 'D' in freq_str:
                            timeframe = f"{freq_str.replace('D', '')}d"
                        elif 'T' in freq_str or 'min' in freq_str.lower():
                            timeframe = f"{freq_str.replace('T', '').replace('min', '')}m"

                # Try to import from the new location (src.utils module)
                import src.utils
                output_dir = src.utils.save_consolidated_matrices_html(debug_data, timeframe=timeframe)
                logging.info(f"Saved consolidated matrices HTML to {output_dir}")
            except (ImportError, AttributeError) as e:
                # If that fails, try the old location or skip
                logging.warning(f"Could not save debug matrices: {e}")
                logging.warning("This is non-critical and won't affect strategy execution")

        # Apply fusion signal filtering if enabled
        if use_fusion_signal:
            logging.info("Applying fusion signal regime filtering to daily scores...")
            daily_scores_df = apply_fusion_signal_filtering(
                daily_scores_df=daily_scores_df,
                data_dict=data_dict,
                fusion_trend_threshold=fusion_trend_threshold,
                fusion_revert_threshold=fusion_revert_threshold,
                fusion_smoothing_length=fusion_smoothing_length,
                fusion_retain_previous_signal=fusion_retain_previous_signal
            )

        logging.info(f"Finished calculating daily scores. DataFrame shape: {daily_scores_df.shape}")
        return daily_scores_df

    except Exception as e:
        logging.error(f"Error in ratio-based score calculation: {e}", exc_info=True)
        return pd.DataFrame()

def calculate_equity_curve_pinescript_style(
    data_dict: Dict[str, pd.DataFrame],
    daily_scores: pd.DataFrame,
    initial_capital: float = 1.0,
    mtpi_signals: Optional[pd.Series] = None,
    strategy_start_date: Optional[pd.Timestamp] = None,
    transaction_fee_rate: float = 0.001  # Adding 0.1% transaction fee
) -> Tuple[pd.Series, pd.Series, Dict]:
    """
    Calculates the equity curve for the strategy based on daily scores using the exact PineScript logic.
    This function exactly mimics the PineScript f_equity function with equity_mode="MTPI Only".

    Args:
        data_dict: Dictionary of DataFrames {symbol: DataFrame} with OHLCV data.
        daily_scores: DataFrame with asset scores for each day.
        initial_capital: Starting capital amount (default=1.0 for normalized returns)
        mtpi_signals: A time series of MTPI signals indexed by timestamp.
        strategy_start_date: If provided, ensures the equity curve starts with
                           initial_capital on this date.
        transaction_fee_rate: Fee rate applied to all trades (default=0.001 for 0.1%)

    Returns:
        Tuple containing:
        - pd.Series: Equity curve indexed by timestamp.
        - pd.Series: Series indicating which asset is held for each day.
        - Dict: Additional metadata about MTPI filtering statistics and whether MTPI filtering was applied.
    """
    if not data_dict or daily_scores.empty:
        logging.warning("calculate_equity_curve_pinescript_style: Input data is empty.")
        return pd.Series(dtype=float), pd.Series(dtype=str), {"mtpiFilteringApplied": False}

    # Extract common dates between scores and price data
    common_dates = None
    for symbol, df in data_dict.items():
        if common_dates is None:
            common_dates = df.index.intersection(daily_scores.index)
        else:
            common_dates = common_dates.intersection(df.index)

    if common_dates is None or len(common_dates) < 2:
        logging.warning("calculate_equity_curve_pinescript_style: Not enough common dates for simulation.")
        return pd.Series(dtype=float), pd.Series(dtype=str), {"mtpiFilteringApplied": False}

    common_dates = sorted(common_dates)  # Ensure chronological order
    logging.info(f"Calculating equity curve from {common_dates[0]} to {common_dates[-1]} ({len(common_dates)} days)")

    # Additional metadata for diagnostic purposes
    mtpi_filtering_stats = {
        "totalDays": 0,
        "affectedDays": 0,
        "percentAffected": 0.0
    }

    # Initialize tracking variables
    equity_curve = pd.Series(index=common_dates, dtype=float)
    equity_curve.iloc[0] = initial_capital  # Day 0 equity = initial capital
    best_asset_series = pd.Series(index=common_dates, dtype=str)
    best_asset_series.iloc[0] = ''  # Initialize as no asset

    # If strategy_start_date is provided, we'll normalize the curve later
    normalize_to_start_date = strategy_start_date is not None

    # Calculate daily returns for each asset
    asset_returns = {}
    for symbol, df in data_dict.items():
        # Calculate daily returns as (today's close / yesterday's close) - 1
        returns = df['close'].pct_change().fillna(0)
        asset_returns[symbol] = returns

    # Handle potential MTPI signal timestamp alignment issues
    if mtpi_signals is not None:
        # Check for timezone issues
        if mtpi_signals.index.tz is None and common_dates[0].tz is not None:
            logging.warning("MTPI signals index has no timezone but data index does. Localizing MTPI signals.")
            mtpi_signals.index = mtpi_signals.index.tz_localize('UTC')
        elif mtpi_signals.index.tz is not None and common_dates[0].tz is None:
            logging.warning("Data index has no timezone but MTPI signals do. Localizing data index.")
            # This is unlikely but included for completeness

        # Check for data frequency misalignment (especially important for 4h timeframe)
        # Get timeframe from first data dictionary item
        data_freq = None
        if data_dict:
            first_symbol = next(iter(data_dict))
            first_df = data_dict[first_symbol]
            if len(first_df) >= 2:
                # Try to infer frequency
                first_diff = first_df.index[1] - first_df.index[0]
                if first_diff.seconds == 14400:  # 4 hours in seconds
                    data_freq = '4h'
                    logging.info(f"Detected 4h timeframe from data")
                elif first_diff.seconds == 3600:  # 1 hour
                    data_freq = '1h'
                elif first_diff.seconds == 86400:  # 1 day
                    data_freq = '1d'

        # Check if signals need to be resampled to match data frequency
        signal_resampled = False
        if data_freq and len(mtpi_signals) >= 2:
            signal_diff = mtpi_signals.index[1] - mtpi_signals.index[0]
            if data_freq == '4h' and signal_diff.seconds != 14400:
                logging.warning(f"MTPI signals frequency ({signal_diff}) doesn't match data frequency (4h). This could cause alignment issues.")

                # Look specifically for signals on or before each common date
                aligned_mtpi_signals = pd.Series(index=common_dates, dtype=int)
                for i, date in enumerate(common_dates):
                    prior_signals = mtpi_signals[mtpi_signals.index <= date]
                    if not prior_signals.empty:
                        aligned_mtpi_signals.iloc[i] = prior_signals.iloc[-1]
                    else:
                        aligned_mtpi_signals.iloc[i] = 0  # Default to neutral if no signal

                mtpi_signals = aligned_mtpi_signals
                signal_resampled = True
                logging.info(f"Resampled MTPI signals to match equity curve dates ({len(mtpi_signals)} signals)")

        # Special check for the first date in the analysis - are there signals on or before it?
        if not signal_resampled:
            first_date = common_dates[0]
            prior_signals = mtpi_signals[mtpi_signals.index <= first_date]
            if prior_signals.empty:
                logging.warning(f"No MTPI signals found on or before the first date ({first_date}). First signals will be missing.")

                # Create a default signal at the beginning to prevent missing signals
                if len(mtpi_signals) > 0:
                    first_signal = mtpi_signals.iloc[0]
                    new_signals = pd.Series([first_signal], index=[first_date])
                    mtpi_signals = pd.concat([new_signals, mtpi_signals])
                    logging.info(f"Added default signal {first_signal} at {first_date}")

    # Check MTPI signal validity - log distribution to detect potential issues
    if mtpi_signals is not None:
        # Find relevant MTPI signals that overlap with our common dates
        relevant_signals = mtpi_signals[mtpi_signals.index <= common_dates[-1]]
        if not relevant_signals.empty:
            # Log signal distribution
            signal_counts = relevant_signals.value_counts()
            bullish_count = signal_counts.get(1, 0)
            bearish_count = signal_counts.get(-1, 0)
            neutral_count = signal_counts.get(0, 0)

            # Calculate percentages
            total_signals = len(relevant_signals)
            bullish_pct = (bullish_count / total_signals * 100) if total_signals > 0 else 0
            bearish_pct = (bearish_count / total_signals * 100) if total_signals > 0 else 0
            neutral_pct = (neutral_count / total_signals * 100) if total_signals > 0 else 0

            logging.info(f"MTPI Signal Distribution: Bullish {bullish_pct:.1f}%, "
                        f"Bearish {bearish_pct:.1f}%, Neutral {neutral_pct:.1f}%")

            # Find signals around the analysis start, if strategy_start_date is provided
            if strategy_start_date is not None:
                # Find the closest signal to strategy_start_date
                try:
                    # Look for signals within 5 days before and after the strategy start date
                    start_window_begin = strategy_start_date - pd.Timedelta(days=5)
                    start_window_end = strategy_start_date + pd.Timedelta(days=5)

                    window_signals = mtpi_signals[(mtpi_signals.index >= start_window_begin) &
                                                (mtpi_signals.index <= start_window_end)]

                    if not window_signals.empty:
                        logging.info(f"\n==== MTPI SIGNALS AROUND ANALYSIS START ({strategy_start_date}) ====")
                        for idx, signal_val in window_signals.items():
                            time_diff = idx - strategy_start_date
                            direction = "before" if time_diff.total_seconds() < 0 else "after"
                            abs_diff = abs(time_diff.total_seconds())
                            time_str = f"{abs_diff/3600:.1f} hours" if abs_diff < 86400 else f"{abs_diff/86400:.1f} days"

                            logging.info(f"  {idx}: MTPI={signal_val} ({time_str} {direction} start)")

                        # Find the last signal before or equal to the start date
                        pre_start_signals = mtpi_signals[mtpi_signals.index <= strategy_start_date]
                        if not pre_start_signals.empty:
                            last_signal_before_start = pre_start_signals.iloc[-1]
                            last_signal_idx = pre_start_signals.index[-1]
                            time_diff = strategy_start_date - last_signal_idx
                            time_str = f"{time_diff.total_seconds()/3600:.1f} hours" if time_diff.total_seconds() < 86400 else f"{time_diff.total_seconds()/86400:.1f} days"

                            logging.info(f"Last signal before analysis start: MTPI={last_signal_before_start} at {last_signal_idx} ({time_str} before start)")
                            if last_signal_before_start == 1:
                                logging.info(f"This signal would allow trading at the start of analysis")
                            else:
                                logging.info(f"This signal would NOT allow trading at the start of analysis")
                    else:
                        logging.warning(f"No MTPI signals found within +/- 5 days of the analysis start date")
                except Exception as e:
                    logging.error(f"Error analyzing signals around start date: {e}")

            # Detect potential warm-up issues - if signals are too skewed at the beginning
            first_10pct = relevant_signals.iloc[:int(len(relevant_signals)*0.1)]
            if not first_10pct.empty:
                first_10pct_counts = first_10pct.value_counts()
                first_bullish = first_10pct_counts.get(1, 0)
                first_bearish = first_10pct_counts.get(-1, 0)
                first_neutral = first_10pct_counts.get(0, 0)

                # If the first 10% of signals are > 90% one type, it might indicate warmup issues
                if len(first_10pct) > 0:
                    most_common = max(first_bullish, first_bearish, first_neutral)
                    if most_common / len(first_10pct) > 0.9:
                        signal_type = "bullish" if most_common == first_bullish else "bearish" if most_common == first_bearish else "neutral"
                        logging.warning(f"Potential warm-up issue detected: First 10% of MTPI signals are {most_common/len(first_10pct)*100:.1f}% {signal_type}")
                        logging.warning("Consider using a longer warm-up period or adjusting the signal generation parameters")
        else:
            logging.warning("No MTPI signals available for the analysis period")

    # Count trades for debugging
    trade_count = 0
    days_in_market = 0

    # Track current asset for fee calculations
    current_asset = None

    # Loop through each day (starting from the second day)
    for i in range(1, len(common_dates)):
        yesterday = common_dates[i-1]
        today = common_dates[i]

        # Find the best asset for yesterday
        yesterday_scores = daily_scores.loc[yesterday]
        best_asset = find_best_asset_for_day(yesterday_scores.to_dict())
        best_asset_series.loc[yesterday] = best_asset

        # Check if MTPI signal allows trading
        mtpi_allows_trade = True
        if mtpi_signals is not None:
            # Find the last known MTPI signal on or before yesterday
            prior_signals = mtpi_signals[mtpi_signals.index <= yesterday]
            if not prior_signals.empty:
                latest_signal = prior_signals.iloc[-1]
                latest_signal_time = prior_signals.index[-1]
                mtpi_allows_trade = (latest_signal == 1)

                # Enhanced MTPI signal logging for debugging
                time_diff = yesterday - latest_signal_time
                time_diff_hours = time_diff.total_seconds() / 3600

                if mtpi_allows_trade:
                    logging.info(f"MTPI CHECK DAY {yesterday}: Using signal {latest_signal} from {latest_signal_time} ({time_diff_hours:.1f}h old) - TRADING ALLOWED")
                else:
                    logging.info(f"MTPI CHECK DAY {yesterday}: Using signal {latest_signal} from {latest_signal_time} ({time_diff_hours:.1f}h old) - TRADING NOT ALLOWED (will keep equity flat)")

                # For debugging, extract the exact timestamp of the signal used
                if time_diff_hours > 4:  # More than one 4h candle apart
                    logging.debug(f"Signal used for {yesterday} is from {latest_signal_time} ({time_diff_hours:.1f} hours old)")

                # For debugging and tracking signal transitions
                if i > 1 and mtpi_allows_trade != (equity_curve.iloc[i-1] != equity_curve.iloc[i-2]):
                    # Signal state changed
                    if mtpi_allows_trade:
                        logging.debug(f"MTPI signal turned bullish at {yesterday}, entering market")
                    else:
                        logging.debug(f"MTPI signal turned bearish/neutral at {yesterday}, exiting market")
            else:
                mtpi_allows_trade = False
                logging.debug(f"No MTPI signal available for {yesterday}, staying out of market")

        # Get the return for the best asset
        asset_return = 0.0
        if best_asset and best_asset in asset_returns:
            asset_return = asset_returns[best_asset].loc[today]

        # Calculate equity using PineScript logic with fee consideration:
        if i == 1:
            # First day calculation
            if mtpi_allows_trade and best_asset:
                # Apply fee if we're entering a new position
                if current_asset != best_asset:
                    # Apply transaction fee for the initial purchase
                    fee_factor = 1 - transaction_fee_rate
                    equity_curve.iloc[i] = equity_curve.iloc[i-1] * fee_factor * (1 + asset_return)
                    current_asset = best_asset
                    logging.debug(f"Initial trade into {best_asset} with fee: {transaction_fee_rate*100}%, fee_factor: {fee_factor}")
                else:
                    equity_curve.iloc[i] = equity_curve.iloc[i-1] * (1 + asset_return)

                days_in_market += 1
                if best_asset_series.iloc[i-1] != best_asset:
                    trade_count += 1
            else:
                equity_curve.iloc[i] = equity_curve.iloc[i-1]  # No change if MTPI doesn't allow trade
                current_asset = None  # Reset current asset since we're out of market
        else:
            # Subsequent days
            if mtpi_allows_trade and best_asset:
                if best_asset != current_asset:
                    # Asset change happened - apply fee
                    if current_asset is not None:
                        # Switching assets: sell existing + buy new (2 fees)
                        fee_factor = (1 - transaction_fee_rate) * (1 - transaction_fee_rate)
                        logging.debug(f"Switching from {current_asset} to {best_asset} with fee: {transaction_fee_rate*100}% x2, fee_factor: {fee_factor}")
                    else:
                        # Just buying (1 fee)
                        fee_factor = (1 - transaction_fee_rate)
                        logging.debug(f"Entering market with {best_asset} with fee: {transaction_fee_rate*100}%, fee_factor: {fee_factor}")

                    equity_curve.iloc[i] = equity_curve.iloc[i-1] * fee_factor * (1 + asset_return)
                    current_asset = best_asset
                    trade_count += 1
                else:
                    # Continuing to hold same asset - no fee
                    equity_curve.iloc[i] = equity_curve.iloc[i-1] * (1 + asset_return)

                days_in_market += 1
            else:
                # CRITICAL FIX: Ensure absolutely flat equity curve during non-trading periods
                # Regardless of whether we're currently in the market or not, just keep the same value
                # This addresses the inconsistency where some price movements were being allowed
                equity_curve.iloc[i] = equity_curve.iloc[i-1]

                # If we were previously in the market, consider this as exiting (for tracking purposes only)
                if current_asset is not None:
                    logging.debug(f"Exiting market from {current_asset} due to MTPI signal")
                    current_asset = None
                    trade_count += 1

    # Fill in the last day's asset
    if len(common_dates) > 0:
        last_day = common_dates[-1]
        # Check MTPI signal for the last day
        mtpi_allows_trade = True
        if mtpi_signals is not None:
            prior_signals = mtpi_signals[mtpi_signals.index <= last_day]
            if not prior_signals.empty:
                latest_signal = prior_signals.iloc[-1]
                mtpi_allows_trade = (latest_signal == 1)

        if mtpi_allows_trade:
            last_scores = daily_scores.loc[last_day]
            best_asset_series.loc[last_day] = find_best_asset_for_day(last_scores.to_dict())
        else:
            best_asset_series.loc[last_day] = ''  # No asset if MTPI signal doesn't allow it

    # Log trade statistics
    days_out_of_market = len(common_dates) - days_in_market
    pct_in_market = (days_in_market / len(common_dates)) * 100 if len(common_dates) > 0 else 0

    logging.info(f"Strategy statistics: {trade_count} trades, {days_in_market} days in market ({pct_in_market:.1f}%), "
                f"{days_out_of_market} days out of market ({100-pct_in_market:.1f}%)")

    # Check for NaNs or other anomalies in the final curves
    if equity_curve.isnull().any():
        logging.warning(f"Equity curve contains {equity_curve.isnull().sum()} NaN values.")
        equity_curve = equity_curve.fillna(method='ffill')

    # If strategy_start_date is provided, normalize the curve to start with initial_capital on that date
    if normalize_to_start_date and strategy_start_date in equity_curve.index:
        # Find the value on the strategy_start_date
        start_value = equity_curve.loc[strategy_start_date]
        if start_value > 0:  # Avoid division by zero
            # Calculate the normalization factor
            normalization_factor = initial_capital / start_value
            # Apply the normalization factor to the entire curve
            equity_curve = equity_curve * normalization_factor
            logging.info(f"Normalized equity curve to start with {initial_capital} on {strategy_start_date}")
        else:
            logging.warning(f"Cannot normalize equity curve: value on {strategy_start_date} is {start_value}")

    # Additional metadata for diagnostic purposes
    mtpi_filtering_stats["totalDays"] = len(common_dates) - 1  # Excluding first day
    mtpi_filtering_stats["affectedDays"] = len(common_dates) - days_in_market  # Days out of market
    mtpi_filtering_stats["percentAffected"] = (mtpi_filtering_stats["affectedDays"] / mtpi_filtering_stats["totalDays"]) * 100

    # Additional metadata for diagnostic purposes
    metadata = {
        "mtpiFilteringApplied": True,
        "mtpiFilteringInfo": mtpi_filtering_stats
    }

    return equity_curve, best_asset_series, metadata

def calculate_equity_curve(
    data_dict: Dict[str, pd.DataFrame],
    daily_scores: pd.DataFrame,
    initial_capital: float = 1.0,
    start_with_best_asset: bool = True,
    use_mtpi_signal: bool = False,
    mtpi_signals: Optional[pd.Series] = None,
    strategy_start_date: Optional[pd.Timestamp] = None,
    transaction_fee_rate: float = 0.001  # Adding 0.1% transaction fee
) -> Tuple[pd.Series, pd.Series]:
    """
    Calculates the equity curve for the strategy based on daily scores.

    This simulates investing in the asset with the highest score each day.
    If multiple assets tie for the highest score, we select one deterministically.

    Args:
        data_dict: Dictionary of DataFrames {symbol: DataFrame} with OHLCV data.
        daily_scores: DataFrame with asset scores for each day.
        initial_capital: Starting capital amount (default=1.0 for normalized returns)
        start_with_best_asset: If True, forces the strategy to start with the best
                              performing asset based on the first day's scores.
        use_mtpi_signal: Whether to use the MTPI signal to filter asset selection.
                        If True, only invest when MTPI signal is positive (1).
        mtpi_signals: A time series of MTPI signals indexed by timestamp. Used if
                     use_mtpi_signal is True. Will only invest when signal is 1.
        strategy_start_date: Optional start date for the strategy.
        transaction_fee_rate: Fee rate applied to all trades (default=0.001 for 0.1%)

    Returns:
        Tuple containing:
        - pd.Series: Equity curve indexed by timestamp.
        - pd.Series: Series indicating which asset is held for each day.
                    Empty string ('') indicates no asset is held (out of market).
    """
    if not data_dict or daily_scores.empty:
        logging.warning("calculate_equity_curve: Input data is empty.")
        return pd.Series(dtype=float), pd.Series(dtype=str)

    # Extract common dates between scores and price data
    common_dates = None
    for symbol, df in data_dict.items():
        if common_dates is None:
            common_dates = df.index.intersection(daily_scores.index)
        else:
            common_dates = common_dates.intersection(df.index)

    if common_dates is None or len(common_dates) < 2:
        logging.warning("calculate_equity_curve: Not enough common dates for simulation.")
        return pd.Series(dtype=float), pd.Series(dtype=str)

    common_dates = sorted(common_dates)  # Ensure chronological order
    logging.info(f"Calculating equity curve from {common_dates[0]} to {common_dates[-1]} ({len(common_dates)} days)")

    # Initialize tracking variables
    equity_curve = pd.Series(index=common_dates, dtype=float)
    equity_curve.iloc[0] = initial_capital  # Day 0 equity = initial capital
    best_asset_series = pd.Series(index=common_dates, dtype=str)
    best_asset_series.iloc[0] = ''  # Initialize as no asset

    # Set up initial position
    current_asset = None
    current_units = initial_capital  # Start with cash

    # If strategy_start_date is provided, we'll normalize the curve later
    normalize_to_start_date = strategy_start_date is not None

    # If starting with an asset and allowed by MTPI signal
    if start_with_best_asset and len(common_dates) > 0:
        first_day = common_dates[0]

        # Check MTPI signal for the first day
        mtpi_allows_trade = True
        if use_mtpi_signal and mtpi_signals is not None:
            # Find the last known MTPI signal on or before the first day
            prior_signals = mtpi_signals[mtpi_signals.index <= first_day]
            if not prior_signals.empty:
                latest_signal = prior_signals.iloc[-1]
                latest_signal_time = prior_signals.index[-1]
                mtpi_allows_trade = (latest_signal == 1)
                logging.info(f"STRATEGY INIT: First day {first_day} - MTPI signal from {latest_signal_time} is {latest_signal}, allows trade: {mtpi_allows_trade}")
            else:
                logging.warning(f"No MTPI signal available for {first_day}. Assuming no trade allowed.")
                mtpi_allows_trade = False

        if mtpi_allows_trade:
            # Get the first day's scores and find best asset
            first_day_scores = daily_scores.loc[first_day]
            best_initial_asset = find_best_asset_for_day(first_day_scores.to_dict())

            if best_initial_asset != '':
                logging.info(f"Starting strategy with best asset: {best_initial_asset}")
                current_asset = best_initial_asset
                best_asset_series.iloc[0] = current_asset

                # Calculate initial units based on first day's price
                first_day_price = data_dict[current_asset].loc[first_day, 'close']

                # Apply transaction fee when purchasing the initial asset
                capital_after_fee = initial_capital * (1 - transaction_fee_rate)
                current_units = capital_after_fee / first_day_price

                logging.info(f"Initial position: {capital_after_fee} (after {transaction_fee_rate*100}% fee) / {first_day_price} = {current_units} units")
            else:
                logging.info("No valid asset found for first day. Starting with cash.")
        else:
            logging.info("MTPI signal doesn't allow trade on the first day. Starting with cash.")

    # Update first day's equity based on whether we're holding an asset or not
    if current_asset is not None and current_asset != '':
        first_day_price = data_dict[current_asset].loc[common_dates[0], 'close']
        equity_curve.iloc[0] = current_units * first_day_price
    else:
        equity_curve.iloc[0] = current_units  # Cash value

    # Calculate daily returns
    for i in range(1, len(common_dates)):
        yesterday = common_dates[i-1]
        today = common_dates[i]

        # Get yesterday's scores
        yesterday_scores = daily_scores.loc[yesterday]

        # Check MTPI signal if enabled
        mtpi_allows_trade = True
        if use_mtpi_signal and mtpi_signals is not None:
            # Find the last known MTPI signal on or before yesterday
            prior_signals = mtpi_signals[mtpi_signals.index <= yesterday]
            if not prior_signals.empty:
                latest_signal = prior_signals.iloc[-1]
                latest_signal_time = prior_signals.index[-1]
                mtpi_allows_trade = (latest_signal == 1)

                # Log detailed information about the MTPI signal decision
                time_diff = yesterday - latest_signal_time
                time_diff_hours = time_diff.total_seconds() / 3600

                if mtpi_allows_trade:
                    logging.info(f"TRADE DECISION: Day {yesterday} - MTPI signal from {latest_signal_time} ({time_diff_hours:.1f}h old) is {latest_signal}, ALLOWING trade")
                else:
                    logging.info(f"TRADE DECISION: Day {yesterday} - MTPI signal from {latest_signal_time} ({time_diff_hours:.1f}h old) is {latest_signal}, NOT allowing trade")
            else:
                logging.warning(f"No MTPI signal available for {yesterday}. Assuming no trade allowed.")
                mtpi_allows_trade = False

        # If MTPI allows a trade, find the best asset; otherwise, signal to stay out of market
        if mtpi_allows_trade:
            best_asset = find_best_asset_for_day(yesterday_scores.to_dict())
        else:
            best_asset = ''  # Empty string indicates no asset
            logging.info(f"MTPI signal doesn't allow trade on {yesterday}, staying out of the market")

        best_asset_series.loc[yesterday] = best_asset

        # Handle position changes based on the best asset and MTPI signal
        if not mtpi_allows_trade and current_asset is not None and current_asset != '':
            # MTPI signal changed from bullish to non-bullish - exit position
            logging.info(f"MTPI signal changed to non-bullish, exiting position in {current_asset} on {yesterday}")

            # Calculate the cash value from selling the current asset, applying the fee
            yesterday_price = data_dict[current_asset].loc[yesterday, 'close']
            cash_value = current_units * yesterday_price
            cash_value_after_fee = cash_value * (1 - transaction_fee_rate)  # Apply selling fee

            logging.debug(f"Selling {current_asset}: {current_units} units * {yesterday_price} = {cash_value} (before fee)")
            logging.debug(f"After {transaction_fee_rate*100}% fee: {cash_value_after_fee}")

            # Update position tracking
            current_asset = None
            current_units = cash_value_after_fee

            # Update equity curve with cash position
            equity_curve.loc[today] = current_units

        elif not mtpi_allows_trade:
            # Continue to stay out of the market if MTPI signal doesn't allow trading
            equity_curve.loc[today] = current_units  # Maintain cash position

        elif mtpi_allows_trade and best_asset != current_asset:
            # MTPI allows trading and we need to switch assets or enter the market

            if current_asset is not None and current_asset != '':
                # Sell the current asset first if we're holding one
                yesterday_price = data_dict[current_asset].loc[yesterday, 'close']
                sell_value = current_units * yesterday_price
                cash_value = sell_value * (1 - transaction_fee_rate)  # Apply selling fee

                logging.debug(f"Selling {current_asset}: {current_units} units * {yesterday_price} = {sell_value} (before fee)")
                logging.debug(f"After {transaction_fee_rate*100}% fee: {cash_value}")
            else:
                # We're already in cash, just use the current value
                cash_value = current_units

            if best_asset != '':
                # Buy the new best asset
                yesterday_price = data_dict[best_asset].loc[yesterday, 'close']

                # Apply buying fee
                buy_value = cash_value * (1 - transaction_fee_rate)
                current_units = buy_value / yesterday_price
                current_asset = best_asset

                logging.debug(f"Buying {best_asset}: {cash_value} * (1-{transaction_fee_rate*100}%) / {yesterday_price} = {current_units} units")

                # Calculate today's equity based on the new position
                today_price = data_dict[current_asset].loc[today, 'close']
                equity_curve.loc[today] = current_units * today_price
            else:
                # Best asset is empty string, stay in cash
                current_asset = None
                equity_curve.loc[today] = current_units

        elif current_asset is not None and current_asset != '':
            # Continue holding the same asset
            today_price = data_dict[current_asset].loc[today, 'close']
            equity_curve.loc[today] = current_units * today_price
        else:
            # Continue holding cash
            equity_curve.loc[today] = current_units

    # Fill in the last day's asset
    if len(common_dates) > 0:
        last_day = common_dates[-1]
        # Check MTPI signal for the last day
        mtpi_allows_trade = True
        if use_mtpi_signal and mtpi_signals is not None:
            prior_signals = mtpi_signals[mtpi_signals.index <= last_day]
            if not prior_signals.empty:
                latest_signal = prior_signals.iloc[-1]
                mtpi_allows_trade = (latest_signal == 1)

        if mtpi_allows_trade:
            last_scores = daily_scores.loc[last_day]
            best_asset_series.loc[last_day] = find_best_asset_for_day(last_scores.to_dict())
        else:
            best_asset_series.loc[last_day] = ''  # No asset if MTPI signal doesn't allow it

    # Check for NaNs or other anomalies in the final curves
    if equity_curve.isnull().any():
        logging.warning(f"Equity curve contains {equity_curve.isnull().sum()} NaN values.")
        # Optionally handle NaNs (e.g., forward fill)
        equity_curve = equity_curve.fillna(method='ffill')

    # If strategy_start_date is provided, normalize the curve to start with initial_capital on that date
    if normalize_to_start_date and strategy_start_date in equity_curve.index:
        # Find the value on the strategy_start_date
        start_value = equity_curve.loc[strategy_start_date]
        if start_value > 0:  # Avoid division by zero
            # Calculate the normalization factor
            normalization_factor = initial_capital / start_value
            # Apply the normalization factor to the entire curve
            equity_curve = equity_curve * normalization_factor
            logging.info(f"Normalized equity curve to start with {initial_capital} on {strategy_start_date}")
        else:
            logging.warning(f"Cannot normalize equity curve: value on {strategy_start_date} is {start_value}")

    return equity_curve, best_asset_series

def calculate_buy_and_hold_curves(
    data_dict: Dict[str, pd.DataFrame],
    initial_capital: float = 1.0,
    normalize_to_strategy_start: bool = True,
    strategy_start_date: pd.Timestamp = None,
    analysis_end_date: pd.Timestamp = None,
    transaction_fee_rate: float = 0.001  # Adding 0.1% transaction fee
) -> Dict[str, pd.Series]:
    """
    Calculates equity curves for buy-and-hold strategies for each asset.
    For assets that start after the strategy start date, their curves will begin
    from their actual first available date with the initial capital amount.

    Args:
        data_dict: Dictionary of DataFrames {symbol: DataFrame} with OHLCV data.
        initial_capital: Starting capital amount (default=1.0 for normalized returns)
        normalize_to_strategy_start: If True, normalizes all curves to start from the same
                                    value at the strategy start date. For newer assets,
                                    they start from their first available date.
        strategy_start_date: The date to use for normalization. If None, uses the first
                           common date across all assets.
        analysis_end_date: The date to end the analysis. If None, uses all available data.
        transaction_fee_rate: Fee rate applied to buy trade (default=0.001 for 0.1%)

    Returns:
        Dictionary of equity curves {symbol: equity_series} for each asset.
    """
    # Initialize result dictionary
    bnh_curves = {}

    # Find common start date if normalizing
    if normalize_to_strategy_start:
        if strategy_start_date is None:
            # Find the earliest common date across all assets
            common_dates = None
            for df in data_dict.values():
                if common_dates is None:
                    common_dates = df.index
                else:
                    common_dates = common_dates.intersection(df.index)

            if common_dates is not None and not common_dates.empty:
                strategy_start_date = common_dates.min()
                logging.info(f"Using common start date for normalization: {strategy_start_date}")
            else:
                logging.warning("No common dates found across assets. Normalization disabled.")
                normalize_to_strategy_start = False
        else:
            logging.info(f"Using provided start date for normalization: {strategy_start_date}")

    # For each asset, calculate a simple buy-and-hold equity curve
    for symbol, df in data_dict.items():
        if 'close' not in df.columns or len(df) < 2:
            logging.warning(f"Skipping B&H calculation for {symbol}: No 'close' column or insufficient data.")
            continue

        # Determine the actual start date for this asset
        asset_first_date = df.index.min()

        # Ensure timezone consistency
        if strategy_start_date is not None:
            if asset_first_date.tz is None and strategy_start_date.tz is not None:
                asset_first_date = asset_first_date.tz_localize(strategy_start_date.tz)
            elif asset_first_date.tz is not None and strategy_start_date.tz is None:
                strategy_start_date = strategy_start_date.tz_localize(asset_first_date.tz)

        if normalize_to_strategy_start and strategy_start_date is not None:
            # Check if asset has data from the strategy start date
            if asset_first_date <= strategy_start_date:
                # Asset has data from strategy start date, use strategy start date
                actual_start_date = strategy_start_date
                valid_dates = df.index[df.index >= strategy_start_date]
            else:
                # Asset starts later than strategy start date, use asset's first date
                actual_start_date = asset_first_date
                valid_dates = df.index
                logging.info(f"Asset {symbol} starts later ({asset_first_date.date()}) than strategy start ({strategy_start_date.date()}). Using asset's first date.")

            if len(valid_dates) == 0:
                logging.warning(f"No valid data for {symbol}. Skipping.")
                continue

            # Find the first date with non-NaN close price
            start_date = None
            start_price = None
            for date in valid_dates:
                price = df.loc[date, 'close']
                if pd.notna(price) and price > 0:
                    start_date = date
                    start_price = price
                    break

            if start_date is None or start_price is None:
                logging.warning(f"No valid non-NaN close price found for {symbol} from {valid_dates[0]}. Skipping.")
                continue

            # Apply transaction fee to initial purchase
            capital_after_fee = initial_capital * (1 - transaction_fee_rate)
            units = capital_after_fee / start_price

            # Calculate equity series only from the start date onwards
            equity_series = pd.Series(index=df.index, dtype=float)
            # Fill values only from start_date onwards, and up to analysis_end_date if specified
            if analysis_end_date is not None:
                valid_mask = (df.index >= start_date) & (df.index <= analysis_end_date)
            else:
                valid_mask = df.index >= start_date
            equity_series[valid_mask] = df.loc[valid_mask, 'close'] * units

            logging.info(f"Normalized {symbol} B&H curve to start with {initial_capital} on {start_date}, "
                        f"applying {transaction_fee_rate*100}% fee, resulting in {capital_after_fee} actual investment")
        else:
            # Original behavior - start from the first available date with non-NaN price
            start_date = None
            start_price = None
            for i, price in enumerate(df['close']):
                if pd.notna(price) and price > 0:
                    start_date = df.index[i]
                    start_price = price
                    break

            if start_date is None or start_price is None:
                logging.warning(f"No valid non-NaN close price found for {symbol}. Skipping.")
                continue

            # Apply transaction fee to initial purchase
            capital_after_fee = initial_capital * (1 - transaction_fee_rate)
            units = capital_after_fee / start_price

            # Calculate equity series only from the start date onwards
            equity_series = pd.Series(index=df.index, dtype=float)
            # Fill values only from start_date onwards, and up to analysis_end_date if specified
            if analysis_end_date is not None:
                valid_mask = (df.index >= start_date) & (df.index <= analysis_end_date)
            else:
                valid_mask = df.index >= start_date
            equity_series[valid_mask] = df.loc[valid_mask, 'close'] * units

            logging.info(f"Created {symbol} B&H curve starting from first available date {start_date}, "
                        f"applying {transaction_fee_rate*100}% fee, resulting in {capital_after_fee} actual investment")

        # Store in dictionary
        bnh_curves[symbol] = equity_series

    return bnh_curves

def backtest_strategy(
    data_dict: Dict[str, pd.DataFrame],
    signal_col: str = 'pgo_signal',
    initial_capital: float = 1.0,
    use_btc_pgo_signal: bool = False,  # Kept for backward compatibility
    btc_pgo_signal: Optional[int] = None,  # Kept for backward compatibility
    use_mtpi_signal: bool = False,
    mtpi_signals: Optional[pd.Series] = None,
    transaction_fee_rate: float = 0.001  # Adding 0.1% transaction fee
) -> Dict:
    """
    Runs a complete backtest of the asset rotation strategy.

    Args:
        data_dict: Dictionary of DataFrames {symbol: DataFrame} with OHLCV data.
        signal_col: Column name containing the signal indicator.
        initial_capital: Starting capital amount.
        use_btc_pgo_signal: (Deprecated) Whether to use BTC PGO signal to filter trades.
        btc_pgo_signal: (Deprecated) The BTC PGO signal value.
        use_mtpi_signal: Whether to use MTPI signals to filter trades.
        mtpi_signals: Time series of MTPI signals indexed by timestamp.
        transaction_fee_rate: Fee rate applied to all trades (default=0.001 for 0.1%)

    Returns:
        Dict containing backtest results:
        - daily_scores: DataFrame with daily asset scores.
        - equity_curve: Series with strategy equity values.
        - best_asset_series: Series showing which asset was held on each day.
        - metrics: Dict with performance metrics.
    """
    from .performance import calculate_all_metrics  # Import here to avoid circular import

    results = {}

    try:
        # For backward compatibility
        effective_use_mtpi = use_mtpi_signal or use_btc_pgo_signal
        effective_mtpi_signal = 1  # Default to positive if we need a single value

        # Calculate daily scores
        daily_scores = calculate_daily_scores(
            data_dict=data_dict,
            pgo_signal_col=signal_col,
            use_mtpi_signal=effective_use_mtpi,
            mtpi_signals=mtpi_signals
        )
        if daily_scores.empty:
            logging.error("Backtest failed: No daily scores calculated.")
            return {'error': 'Failed to calculate daily scores.'}
        results['daily_scores'] = daily_scores

        # Store the MTPI signal in the results if used
        if effective_use_mtpi:
            results['mtpi_signal'] = effective_mtpi_signal

        # Calculate equity curve and asset selection series
        equity_curve, best_asset_series, mtpi_filtering_metadata = calculate_equity_curve_pinescript_style(
            data_dict=data_dict,
            daily_scores=daily_scores,
            initial_capital=initial_capital,
            mtpi_signals=mtpi_signals,
            strategy_start_date=None,  # No specific start date for backtest
            transaction_fee_rate=transaction_fee_rate
        )
        if equity_curve.empty:
            logging.error("Backtest failed: No equity curve generated.")
            return {'error': 'Failed to generate equity curve.', 'daily_scores': daily_scores}
        results['equity_curve'] = equity_curve
        results['best_asset_series'] = best_asset_series
        results['mtpi_filtering_metadata'] = mtpi_filtering_metadata

        # Calculate buy-and-hold curves for comparison
        bnh_curves = calculate_buy_and_hold_curves(
            data_dict,
            initial_capital=initial_capital,
            transaction_fee_rate=transaction_fee_rate  # Pass the transaction fee rate
        )
        results['bnh_curves'] = bnh_curves

        # Calculate performance metrics
        metrics = calculate_all_metrics(equity_curve, best_asset_series=results.get('best_asset_series'))
        results['metrics'] = metrics

        # Calculate B&H metrics for comparison
        bnh_metrics = {}
        for symbol, curve in bnh_curves.items():
            bnh_metrics[symbol] = calculate_all_metrics(curve)
        results['bnh_metrics'] = bnh_metrics

        logging.info(f"Backtest completed successfully with {transaction_fee_rate*100}% transaction fee applied.")
        return results

    except Exception as e:
        logging.error(f"Exception during backtest: {e}", exc_info=True)
        results['error'] = str(e)
        return results


def apply_fusion_signal_filtering(
    daily_scores_df: pd.DataFrame,
    data_dict: Dict[str, pd.DataFrame],
    fusion_trend_threshold: float = 0.1,
    fusion_revert_threshold: float = -0.1,
    fusion_smoothing_length: int = 14,
    fusion_retain_previous_signal: bool = True
) -> pd.DataFrame:
    """
    Apply fusion signal regime filtering to daily scores.

    This function implements the layered logic:
    - entryCondition = selectedtrendMethodLong and is_trending (fusion) and timePeriod
    - exitCondition = (selectedtrendMethodShort or not is_trending) and timePeriod

    Args:
        daily_scores_df: DataFrame with daily scores for each asset
        data_dict: Dictionary of asset OHLCV data
        fusion_trend_threshold: Threshold for trending regime detection
        fusion_revert_threshold: Threshold for mean-reverting regime detection
        fusion_smoothing_length: Smoothing length for fusion signal aggregation
        fusion_retain_previous_signal: Enable hysteresis logic for fusion signals

    Returns:
        Filtered daily scores DataFrame
    """
    try:
        from .fusion import FusionAggregator, FusionConfig

        # Create fusion configuration with KPSS commented out (disabled)
        fusion_config = FusionConfig(
            # Indicator inclusion flags - KPSS disabled as requested
            include_adx=False,
            include_kpss=False,  # Commented out/disabled as requested
            include_adf=True,
            include_pp=True,
            include_hurst=True,
            include_corr=True,
            include_rpc=True,
            include_garch=True,
            include_wavelet=True,
            include_halflife=False,

            # Thresholds
            trend_threshold=fusion_trend_threshold,
            revert_threshold=fusion_revert_threshold,

            # Smoothing
            smoothing_length=fusion_smoothing_length,
            retain_previous_signal=fusion_retain_previous_signal
        )

        # Initialize fusion aggregator
        fusion_aggregator = FusionAggregator(fusion_config)

        # Calculate fusion signals for each asset
        fusion_signals = {}
        for asset_symbol, asset_data in data_dict.items():
            if asset_symbol in daily_scores_df.columns:
                try:
                    # Calculate fusion regime signal for this asset
                    fusion_results = fusion_aggregator.calculate(asset_data)
                    is_trending = fusion_results['is_trending']

                    # Store the trending signal for this asset
                    fusion_signals[asset_symbol] = is_trending

                    logging.info(f"Calculated fusion signal for {asset_symbol}: {is_trending.sum()} trending periods out of {len(is_trending)}")

                except Exception as e:
                    logging.warning(f"Failed to calculate fusion signal for {asset_symbol}: {e}")
                    # Create a default signal (always trending) if fusion calculation fails
                    fusion_signals[asset_symbol] = pd.Series(True, index=asset_data.index)

        # Apply fusion filtering to daily scores
        filtered_scores_df = daily_scores_df.copy()

        for date_idx in daily_scores_df.index:
            for asset_symbol in daily_scores_df.columns:
                if asset_symbol in fusion_signals:
                    # Get the fusion signal for this asset at this date
                    try:
                        is_trending = fusion_signals[asset_symbol].loc[date_idx] if date_idx in fusion_signals[asset_symbol].index else True

                        # Apply layered logic: only keep scores when asset is in trending regime
                        if not is_trending:
                            # If not trending, set score to 0 (exit condition)
                            filtered_scores_df.loc[date_idx, asset_symbol] = 0

                    except (KeyError, IndexError):
                        # If we can't find the fusion signal, keep the original score
                        pass

        # Log filtering statistics
        original_nonzero = (daily_scores_df != 0).sum().sum()
        filtered_nonzero = (filtered_scores_df != 0).sum().sum()
        filtered_out = original_nonzero - filtered_nonzero

        logging.info(f"Fusion filtering results: {original_nonzero} original non-zero scores, "
                    f"{filtered_nonzero} after filtering, {filtered_out} filtered out "
                    f"({(filtered_out/original_nonzero*100):.1f}% reduction)")

        return filtered_scores_df

    except Exception as e:
        logging.error(f"Error applying fusion signal filtering: {e}", exc_info=True)
        logging.warning("Returning original scores without fusion filtering")
        return daily_scores_df